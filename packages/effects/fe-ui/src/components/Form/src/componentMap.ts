import type { Component } from 'vue';

import type { ComponentType } from './types/index';

/**
 * Component list, register here to setting it in the form
 */
// import { StrengthMeter } from '#/components/StrengthMeter';
// import { CountdownInput } from '#/components/CountDown';
import {
  // FeAlert,
  // FeAreaSelect,
  // FeAutoComplete,
  // FeBarcode,
  // FeButton,
  // FeCalculate,
  // FeCascader,
  FeCheckbox,
  FeCheckboxSingle,
  // FeColorPicker,
  // FeCron,
  // FeDatePicker,
  // FeDateRange,
  // FeDepSelect,
  // FeDivider,
  // FeEditor,
  // FeGroupSelect,
  // FeGroupTitle,
  // FeIconPicker,
  // FeIframe,
  FeInput,
  // FeInputGroup,
  // FeInputNumber,
  // FeInputPassword,
  // FeInputSearch,
  // FeInputTable,
  // FeLink,
  // FeLocation,
  // FeMonthPicker,
  // FeNumberRange,
  // FeOpenData,
  FeOrganizeSelect,
  // FePopupAttr,
  // FePopupSelect,
  // FePopupTableSelect,
  // FePosSelect,
  // FeQrcode,
  // FeRadio,
  // FeRate,
  // FeRelationForm,
  // FeRelationFormAttr,
  // FeRoleSelect,
  // FeSelect,
  // FeSign,
  // FeSignature,
  // FeSlider,
  // FeSwitch,
  // FeText,
  // FeTextarea,
  // FeTimePicker,
  // FeTimeRange,
  // FeTreeSelect,
  // FeUploadFile,
  // FeUploadImg,
  // FeUploadImgSingle,
  FeUserSelect,
  // FeUsersSelect,
  // FeWeekPicker,
} from '#/components/Fe';

const componentMap = new Map<ComponentType, Component>();

// componentMap.set('StrengthMeter', StrengthMeter);
// componentMap.set('InputCountDown', CountdownInput);

// componentMap.set('InputGroup', FeInputGroup);
// componentMap.set('InputSearch', FeInputSearch);
// componentMap.set('MonthPicker', FeMonthPicker);
// componentMap.set('WeekPicker', FeWeekPicker);

// componentMap.set('Alert', FeAlert);
// componentMap.set('AreaSelect', FeAreaSelect);
// componentMap.set('AutoComplete', FeAutoComplete);
// componentMap.set('Button', FeButton);
// componentMap.set('Cron', FeCron);
// componentMap.set('Cascader', FeCascader);
// componentMap.set('ColorPicker', FeColorPicker);
componentMap.set('Checkbox', FeCheckbox);
componentMap.set('FeCheckboxSingle', FeCheckboxSingle);
// componentMap.set('DatePicker', FeDatePicker);
// componentMap.set('DateRange', FeDateRange);
// componentMap.set('TimePicker', FeTimePicker);
// componentMap.set('TimeRange', FeTimeRange);
// componentMap.set('Divider', FeDivider);
// componentMap.set('Editor', FeEditor);
// componentMap.set('GroupTitle', FeGroupTitle);
componentMap.set('Input', FeInput);
// componentMap.set('InputPassword', FeInputPassword);
// componentMap.set('Textarea', FeTextarea);
// componentMap.set('InputNumber', FeInputNumber);
// componentMap.set('IconPicker', FeIconPicker);
// componentMap.set('Link', FeLink);
componentMap.set('OrganizeSelect', FeOrganizeSelect);
// componentMap.set('DepSelect', FeDepSelect);
// componentMap.set('PosSelect', FePosSelect);
// componentMap.set('GroupSelect', FeGroupSelect);
// componentMap.set('RoleSelect', FeRoleSelect);
componentMap.set('UserSelect', FeUserSelect);
// componentMap.set('UsersSelect', FeUsersSelect);
// componentMap.set('Qrcode', FeQrcode);
// componentMap.set('Barcode', FeBarcode);
// componentMap.set('Radio', FeRadio);
// componentMap.set('Rate', FeRate);
// componentMap.set('Select', FeSelect);
// componentMap.set('Slider', FeSlider);
// componentMap.set('Sign', FeSign);
// componentMap.set('Signature', FeSignature);
// componentMap.set('Switch', FeSwitch);
// componentMap.set('Text', FeText);
// componentMap.set('TreeSelect', FeTreeSelect);
// componentMap.set('UploadFile', FeUploadFile);
// componentMap.set('UploadImg', FeUploadImg);
// componentMap.set('UploadImgSingle', FeUploadImgSingle);
// componentMap.set('BillRule', FeInput);
// componentMap.set('ModifyUser', FeInput);
// componentMap.set('ModifyTime', FeInput);
// componentMap.set('CreateUser', FeOpenData);
// componentMap.set('CreateTime', FeOpenData);
// componentMap.set('CurrOrganize', FeOpenData);
// componentMap.set('CurrPosition', FeOpenData);
// componentMap.set('RelationForm', FeRelationForm);
// componentMap.set('RelationFormAttr', FeRelationFormAttr);
// componentMap.set('PopupSelect', FePopupSelect);
// componentMap.set('PopupTableSelect', FePopupTableSelect);
// componentMap.set('PopupAttr', FePopupAttr);
// componentMap.set('NumberRange', FeNumberRange);
// componentMap.set('Calculate', FeCalculate);
// componentMap.set('InputTable', FeInputTable);
// componentMap.set('Location', FeLocation);
// componentMap.set('Iframe', FeIframe);

export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export { componentMap };
