import type { PageListParams } from '@vben/types';

import type { OrderProductInfo } from '#/api';

import { requestClient } from '#/api/request';

// 商品信息
export interface Warehouse_inventoryInfo {
  id?: string;
  // 商品名称
  productName?: string;
  // 商品别名
  productAlias?: string;
  // 规格型号
  specifications?: string;
  // 商品编码
  productCode?: string;
  // 计量单位
  measureUnit?: string;
  // 可用重量
  salesOrderName1?: string;
  // 仓库名称
  warehouseName?: string;
  // 仓储运营方
  warehouseName1?: string;
  // 项目名称
  projectName?: string;
  // 项目编号
  projectCode?: string;
  // 采购订单名称
  purchaseOrderName?: string;
  // 采购订单编号
  purchaseOrderCode?: string;
  // 上游企业
  supplierName?: string;
  // 销售订单名称
  salesOrderName?: string;
  // 销售订单编号
  salesOrderCode?: string;
  // 下游企业
  purchaserName?: string;
  totalInboundQuantity?: string;
  totalOutboundQuantity?: string;
  inventoryStockSerialList?: OrderProductInfo[];
}
export async function getInventoryListApi(params: PageListParams) {
  return requestClient.get('/scm/inventory/stock/pageList', { params });
}
export async function getInventoryQueryByDateApi(params: PageListParams) {
  return requestClient.post('/scm/inventory/stock/queryByDate', params);
}
export async function infoInventoryApi(params: Warehouse_inventoryInfo) {
  return requestClient.get<Warehouse_inventoryInfo>('/scm/inventory/stock/detail', { params });
}
