import type { BaseDataParams, PageListParams } from '@vben/types';
import { requestClient } from '#/api/request';
import type { ReceivableVO } from './enter';

export interface ReceivablePoolPageVO extends BaseDataParams {
  //业务类型
  bizType?: string;
  //池保理融资企业
  companyName?: string;
  //主键
  id?: number | undefined;
  //应收账款池到期日
  poolDueDate?: Date;
  //池融资比例
  poolFinanceRate?: number;
  //应收账款池名称
  poolName?: string;
  //池内资产总额
  poolTotalAmount?: number;
  //应收账款池期限（个月）
  poolValidity?: number;
  //关联项目
  projectName?: string;
  //操作状态
  status?: string;
}

export interface ReceivablePoolSearchVO extends PageListParams {
  //应收账款池名称
  poolName?: string;
  //关联项目名称
  projectName?: string;
  //池保理融资企业
  companyName?: string;
  //操作状态
  statusList?: string;
  //应收账款池到期日
  poolDueDate?: string;
}

export interface ReceivablePoolVO {
  //业务类型
  bizType?: string;
  //池保理融资企业ID列表
  companyList?: any[];
  //池保理融资企业
  companyCodeList?: string[];
  //折扣率
  discountRate?: number;
  //是否提交操作
  isSubmit?: boolean;
  //主键
  id?: number | undefined;
  //池融资金额上限（元）
  poolCapitalLimit?: number;
  //应收账款池编号
  poolCode?: string;
  //应收账款池到期日
  poolDueDate?: Date;
  //应收账款池名称
  poolName?: string;
  //池内资产总额
  poolTotalAmount?: number;
  //应收账款池有效期（月）
  poolValidity?: number;
  //项目ID
  projectId?: number;
  //应收账款
  receivableList?: ReceivableVO[];
  //置换记录
  receivablePoolLogsList?: ReceivablePoolLogsVO[];
  //操作状态
  status?: string;
  //水位线金额（元）
  thresholdAmount?: number;
  //方法type
  type?: string;
}

export interface ReceivablePoolLogsVO {
  // 债权人
  creditorName?: string;
  // 债务人
  debtorName?: string;
  // 主键
  id?: number | undefined;
  // 入池时间
  inputTime?: Date;
  // 出池时间
  outputTime?: Date;
  // 应收账款池ID
  poolId?: number;
  // 应收账款金额
  receivableAmount?: number;
  // 应收账款ID
  receivableId?: number;
  // 应收账款名称
  receivableName?: string;
  // 中登网登记状态
  zdStatus?: string;
}

// 获取应收账款池分页列表
export async function getReceivablePoolPageListApi(params: ReceivablePoolSearchVO) {
  return requestClient.get<ReceivablePoolPageVO[]>('/factoring/receivable/pool/page', { params });
}

// 应收账款池详情
export async function infoReceivablePoolApi(id: number) {
  return requestClient.get<ReceivablePoolVO>(`/factoring/receivable/pool/detail/${id}`);
}

// 新增应收账款池
export async function addReceivablePoolApi(data: ReceivablePoolVO) {
  return requestClient.post<string>('/factoring/receivable/pool/add', data);
}

// 编辑应收账款池
export async function editReceivablePoolApi(data: ReceivablePoolVO) {
  return requestClient.post<string>('/factoring/receivable/pool/edit', data);
}

// 删除应收账款池
export async function delReceivablePoolApi(id: string) {
  return requestClient.get(`/factoring/receivable/pool/delete/${id}`);
}
