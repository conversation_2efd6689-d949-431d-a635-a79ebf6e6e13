import type { RouteRecordRaw } from 'vue-router';
import {$t} from "@vben/locales";

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ri:file-cloud-line',
      title: $t('page.cloudDisk.group'),
    },
    name: 'CloudDisk',
    path: '/cloud-disk',
    children: [
      {
        name: 'CloudDiskManage',
        path: '/cloud-disk/manage',
        component: () => import('#/views/cloud-disk/manage/index.vue'),
        meta: {
          icon: 'akar-icons:file',
          title: $t('page.cloudDisk.my'),
        },
      },
      {
        name: 'FileUploaderDemo',
        path: '/cloud-disk/uploader-demo',
        component: () => import('#/views/cloud-disk/manage/components/file-uploader-demo.vue'),
        meta: {
          icon: 'lucide:upload',
          title: '文件上传组件演示',
        },
      },
      {
        name: 'FileUploaderTest',
        path: '/cloud-disk/uploader-test',
        component: () => import('#/views/cloud-disk/manage/components/file-uploader-test.vue'),
        meta: {
          icon: 'lucide:test-tube',
          title: '文件上传组件测试',
        },
      },
      {
        name: 'FileUploaderExample',
        path: '/cloud-disk/uploader-example',
        component: () => import('#/views/cloud-disk/manage/components/file-uploader-example.vue'),
        meta: {
          icon: 'lucide:code',
          title: '文件上传事件示例',
        },
      },
    ],
  }
];
export default routes;
