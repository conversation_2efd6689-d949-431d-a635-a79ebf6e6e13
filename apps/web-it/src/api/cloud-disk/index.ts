import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface DriveFileInfo {
  // 文件后缀名 (冗余字段)
  fileExtension?: string;
  // 附件ID (关联oss_attach.id，文件夹此字段为NULL)
  fileId?: number;
  // 文件/文件夹显示名称 (用户可修改)
  fileName?: string;
  // 文件大小 (单位: B, 冗余字段)
  fileSize?: number;
  // 节点类型 (0:文件夹, 1:文件)
  fileType?: number;
  // 主键
  id?: number;
  // 是否共享标志 0:未共享, 1:已共享
  isShare?: number;
  // 主名称
  mainName?: string;
  // 父级ID (根目录为0)
  parentId?: number;
  // 父节点路径|分隔
  parentPath?: string;
  // 共享时间
  shareTime?: Date;
  // 租户主键
  tenantId?: string;
  // 上传时间
  uploadTime?: Date;
  // 所属用户ID
  userId?: number;
  [property: string]: any;
}

export async function getCloudDiskPageListApi(params: PageListParams & { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/drive/page', { params });
}
export async function getCloudDiskMySharePageListApi(params: PageListParams & { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/drive/shares/page/my', { params });
}
export async function getCloudDiskShareToMePageListApi(
  params: PageListParams & { keyword?: string; parentId: number; shareId?: number },
) {
  return requestClient.get('/infra/drive/shares/page/to-my', { params });
}
export async function getCloudDiskRecyclePageListApi(params: PageListParams & { keyword?: string; parentId: number }) {
  return requestClient.get('/infra/drive/trash/page', { params });
}
export async function createCloudDiskFolderApi(data: { fileName: string; parentId: number }) {
  return requestClient.post('/infra/drive/create-folder', data);
}
export async function shareCloudDiskFileApi(data: { ids: number[]; userIds: number[] }) {
  return requestClient.post('/infra/drive/shares/create', data);
}
export async function getCloudDiskFileInfoApi(params: { id: number }) {
  return requestClient.get('/infra/drive/detail', { params });
}
export async function renameCloudDiskFileApi(params: { fileName: string; id: number }) {
  return requestClient.post('/infra/drive/rename', {}, { params });
}
export async function delCloudDiskFileApi(data: { ids: number[] }) {
  return requestClient.post('/infra/drive/delete-batch', data);
}
export async function getCloudDiskShareDetail(params: { id: number }) {
  return requestClient.get('/infra/drive/shares/detail', { params });
}
export async function editShareCloudDiskFileApi(data: { ids: number[]; userIds: number[] }) {
  return requestClient.post('/infra/drive/shares/adjust', { id: data.ids[0], userIds: data.userIds });
}
export async function cancelCloudDiskShareApi(data: { ids: number[] }) {
  return requestClient.post('/infra/drive/shares/cancel', data);
}

export const downloadFileApi = (data: { ids: number[] }) => {
  return requestClient.post('/cloud-disk/pack-download', data);
};
export const getFolderTreeApi = () => {
  return requestClient.get('/cloud-disk/folder-tree');
};
export const moveToApi = (data: { ids: string; toId: number }) => {
  return requestClient.post('/cloud-disk/create-document', data);
};
export const restoreApi = (ids: number[]) => {
  return requestClient.post('/cloud-disk/folder-tree', ids);
};
export const permanentDelApi = (ids: number[]) => {
  return requestClient.post('/cloud-disk/folder-tree', ids);
};
