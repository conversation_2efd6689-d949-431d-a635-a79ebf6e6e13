# 自定义文件上传组件

## 概述

这是一个基于 Ant Design Vue 的 Upload 组件封装的自定义文件上传组件，提供了丰富的文件上传状态显示、进度条、操作按钮等功能。**重要：该组件完全基于 antdv 的 fileList，不会破坏原有的文件上传机制。**

## 功能特性

### ✨ 核心功能

- **文件名显示**：完整显示文件名，过长时自动截断并显示省略号
- **文件大小格式化**：自动格式化显示文件大小（B、KB、MB、GB）
- **上传状态管理**：支持等待、上传中、成功、失败、暂停等多种状态
- **实时进度条**：上传过程中显示彩色进度条背景
- **操作按钮**：暂停/恢复、重试、移除等操作
- **批量管理**：支持清空所有文件、批量操作

### 🎨 UI 特性

- **现代化设计**：采用卡片式设计，支持悬浮效果
- **状态指示**：不同状态使用不同颜色的图标和背景
- **响应式布局**：适配不同屏幕尺寸
- **平滑动画**：进度条和状态切换都有平滑的过渡动画

## 文件状态

| 状态 | 图标 | 颜色 | 说明 |
|------|------|------|------|
| waiting | 🕐 | #909398 | 等待上传 |
| uploading | ⏳ | #1890ff | 上传中（显示进度百分比） |
| done/success | ✅ | #52c41a | 上传成功 |
| error | ❌ | #ff4d4f | 上传失败 |
| paused | ⏸️ | #faad14 | 已暂停（自定义状态） |

## 使用方法

### 基本用法

```vue
<template>
  <FileUploader 
    :multiple="true"
    :accept="'*'"
    :upload-api="uploadApi"
    :folder-id="0"
    @change="handleUploadChange"
  />
</template>

<script setup>
import FileUploader from './components/file-uploader.vue';

// 上传API函数
const uploadApi = (formData, options) => {
  // 返回Promise，处理文件上传
  return fetch('/api/upload', {
    method: 'POST',
    body: formData,
    headers: options.headers
  }).then(response => response.json());
};

const handleUploadChange = (info) => {
  console.log('Upload change:', info);
};
</script>
```

### 高级用法

```vue
<template>
  <FileUploader
    ref="fileUploaderRef"
    :multiple="true"
    :accept="'.pdf,.doc,.docx'"
    :upload-api="uploadApi"
    :folder-id="currentFolderId"
    @change="handleUploadChange"
    @all-success="handleAllSuccess"
  />

  <!-- 显示统计信息 -->
  <div class="upload-stats">
    <span>总文件数：{{ fileUploaderRef?.fileList?.length || 0 }}</span>
    <span>上传中：{{ fileUploaderRef?.uploadingCount || 0 }}</span>
    <span>成功：{{ fileUploaderRef?.successCount || 0 }}</span>
    <span>失败：{{ fileUploaderRef?.errorCount || 0 }}</span>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FileUploader from './components/file-uploader.vue';

const fileUploaderRef = ref();
const currentFolderId = ref(0);

// 处理所有文件上传成功事件
const handleAllSuccess = (data) => {
  console.log('所有文件上传成功！', data);
  // 可以在这里执行后续操作：
  // 1. 显示成功提示
  // 2. 刷新文件列表
  // 3. 跳转到其他页面
  // 4. 发送通知等
};

// 可以通过 ref 访问组件的方法和属性
const clearAll = () => {
  fileUploaderRef.value?.clearAllFiles();
};
</script>
```

## API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| uploadApi | Function | - | 上传API函数，必需 |
| folderId | Number | 0 | 文件夹ID |
| multiple | Boolean | - | 是否支持多选文件 |
| accept | String | - | 接受上传的文件类型 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 上传文件改变时的状态 | function(info) |
| all-success | 所有文件都上传成功时触发 | function({ files, total, successCount }) |

### Methods

通过 ref 可以访问以下方法：

| 方法名 | 说明 | 参数 |
|--------|------|------|
| clearAllFiles | 清空所有文件 | - |

### Expose

组件暴露的属性：

| 属性名 | 类型 | 说明 |
|--------|------|------|
| fileList | Array | antdv 的文件列表 |
| uploadingCount | number | 上传中的文件数量 |
| successCount | number | 上传成功的文件数量 |
| errorCount | number | 上传失败的文件数量 |

## 重要说明

### 文件列表管理

- **使用 antdv 原生 fileList**：组件使用 `v-model:file-list="fileList"` 绑定，完全兼容 antdv 的文件上传机制
- **自定义状态支持**：通过 `file.customStatus` 属性支持暂停等自定义状态
- **状态优先级**：自定义状态优先于 antdv 原生状态显示

### 全部成功事件

`all-success` 事件会在以下条件满足时触发：

1. **所有文件都完成上传**：没有文件处于 `uploading` 或 `waiting` 状态
2. **全部上传成功**：所有文件的状态都是 `done` 或 `success`
3. **至少有一个文件**：文件列表不为空

**事件数据结构：**
```javascript
{
  files: Array,        // 成功上传的文件列表
  total: Number,       // 总文件数量
  successCount: Number // 成功上传的文件数量
}
```

**触发时机：**
- 文件上传状态改变时（自动检查）
- 手动操作文件后（暂停、恢复、重试、移除）
- 延迟 100ms 执行，确保状态更新完成

### 状态管理

```javascript
// 获取文件显示状态的逻辑
const getFileDisplayStatus = (file) => {
  if (file.customStatus) {
    return file.customStatus;  // 优先使用自定义状态
  }
  return file.status || 'waiting';  // 否则使用 antdv 原生状态
};
```

## 演示页面

访问 `/cloud-disk/uploader-demo` 可以查看完整的演示页面，包含：

- 基本功能演示
- 状态模拟操作
- 实时统计信息
- 功能说明文档

## 注意事项

1. **兼容性**：完全兼容 Ant Design Vue Upload 组件的所有功能
2. **状态管理**：自定义状态通过 `customStatus` 属性实现，不影响原生状态
3. **文件操作**：暂停/恢复功能为演示功能，实际使用需要后端API支持
4. **内存管理**：大量文件上传时注意及时清理已完成的文件

## 扩展建议

1. **真实暂停/恢复**：集成支持暂停恢复的上传库（如 vue-simple-uploader）
2. **文件预览**：添加图片、文档预览功能
3. **断点续传**：集成断点续传功能
4. **拖拽上传**：添加拖拽上传支持
5. **文件分类**：按文件类型分类显示
