<template>
  <div class="file-uploader-test">
    <h2>文件上传组件测试</h2>

    <div class="test-section">
      <h3>基本测试</h3>
      <FileUploader
        ref="fileUploaderRef"
        :multiple="true"
        :accept="'*'"
        :upload-api="testUploadApi"
        :folder-id="0"
        @change="handleChange"
        @all-success="handleAllSuccess"
        @all-completed="handleAllCompleted"
      />
    </div>

    <div class="test-section">
      <h3>事件日志</h3>
      <div class="event-log">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="timestamp">{{ log.timestamp }}</span>
          <span class="event-type" :class="log.type">{{ log.type }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>测试操作</h3>
      <div class="test-actions">
        <Button @click="addTestFiles" type="primary">添加测试文件</Button>
        <Button @click="simulateAllSuccess" type="default">模拟全部成功</Button>
        <Button @click="simulatePartialSuccess" type="default">模拟部分失败</Button>
        <Button @click="clearLogs" type="dashed">清空日志</Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Button } from 'ant-design-vue';
import FileUploader from './file-uploader.vue';

const fileUploaderRef = ref();
const eventLogs = ref<Array<{ timestamp: string; type: string; message: string }>>([]);

// 添加日志
const addLog = (type: string, message: string) => {
  eventLogs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    message
  });

  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50);
  }
};

// 测试上传API
const testUploadApi = (formData: any, options: any) => {
  addLog('API', '开始上传文件');

  return new Promise((resolve, reject) => {
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 25;
      if (options.onUploadProgress) {
        options.onUploadProgress({ loaded: progress, total: 100 });
      }

      if (progress >= 100) {
        clearInterval(timer);
        addLog('API', '文件上传完成');
        resolve({
          code: 200,
          data: { url: 'https://example.com/file.pdf' },
          msg: '上传成功'
        });
      }
    }, 300);
  });
};

// 处理文件变化
const handleChange = (info: any) => {
  addLog('CHANGE', `文件状态变化: ${info.file.name} - ${info.file.status}`);
};

// 处理所有文件上传完成
const handleAllCompleted = (data: any) => {
  addLog('ALL-COMPLETED', `📋 所有文件上传完成！总计: ${data.total}, 成功: ${data.successCount}, 失败: ${data.errorCount}`);
  console.log('All files upload completed:', data);
};

// 处理所有文件上传成功
const handleAllSuccess = (data: any) => {
  addLog('ALL-SUCCESS', `🎉 所有文件上传成功！总计: ${data.total}, 成功: ${data.successCount}`);
  console.log('All files uploaded successfully:', data);
};

// 添加测试文件
const addTestFiles = () => {
  const files = [
    { name: '测试文档.pdf', size: 1024 * 1024 },
    { name: '图片文件.jpg', size: 512 * 1024 },
    { name: '数据表格.xlsx', size: 2 * 1024 * 1024 }
  ];

  files.forEach((fileInfo, index) => {
    const mockFile = {
      uid: `test-${Date.now()}-${index}`,
      name: fileInfo.name,
      size: fileInfo.size,
      status: 'waiting',
      percent: 0,
      originFileObj: new File([''], fileInfo.name, { type: 'application/octet-stream' })
    };

    if (fileUploaderRef.value?.fileList) {
      fileUploaderRef.value.fileList.push(mockFile);
      if (fileUploaderRef.value.state) {
        fileUploaderRef.value.state.show = true;
      }
    }
  });

  addLog('TEST', `添加了 ${files.length} 个测试文件`);
};

// 模拟全部成功
const simulateAllSuccess = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    fileList.forEach(file => {
      file.customStatus = 'done';
      file.percent = 100;
    });

    setTimeout(() => {
      fileUploaderRef.value?.checkAllFilesSuccess?.();
    }, 200);

    addLog('TEST', '模拟所有文件上传成功');
  } else {
    addLog('ERROR', '没有文件可以模拟');
  }
};

// 模拟部分成功
const simulatePartialSuccess = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    fileList.forEach((file, index) => {
      if (index % 2 === 0) {
        file.customStatus = 'done';
        file.percent = 100;
      } else {
        file.customStatus = 'error';
        file.percent = 0;
      }
    });

    setTimeout(() => {
      fileUploaderRef.value?.checkAllFilesSuccess?.();
    }, 200);

    addLog('TEST', '模拟部分文件失败');
  } else {
    addLog('ERROR', '没有文件可以模拟');
  }
};

// 清空日志
const clearLogs = () => {
  eventLogs.value = [];
  addLog('SYSTEM', '日志已清空');
};
</script>

<style lang="less" scoped>
.file-uploader-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #262626;
    margin-bottom: 24px;
  }

  .test-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    h3 {
      color: #262626;
      margin-bottom: 16px;
      font-size: 16px;
    }
  }

  .event-log {
    max-height: 300px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 12px;

    .log-item {
      display: flex;
      align-items: center;
      padding: 4px 0;
      border-bottom: 1px solid #f5f5f5;
      font-size: 12px;

      &:last-child {
        border-bottom: none;
      }

      .timestamp {
        color: #8c8c8c;
        margin-right: 12px;
        min-width: 80px;
      }

      .event-type {
        padding: 2px 8px;
        border-radius: 4px;
        margin-right: 12px;
        min-width: 80px;
        text-align: center;
        font-weight: 500;

        &.CHANGE {
          background: #e6f7ff;
          color: #1890ff;
        }

        &.ALL-SUCCESS {
          background: #f6ffed;
          color: #52c41a;
        }

        &.ALL-COMPLETED {
          background: #f0f5ff;
          color: #2f54eb;
        }

        &.API {
          background: #fff2e8;
          color: #fa8c16;
        }

        &.TEST {
          background: #f9f0ff;
          color: #722ed1;
        }

        &.ERROR {
          background: #fff2f0;
          color: #ff4d4f;
        }

        &.SYSTEM {
          background: #f5f5f5;
          color: #595959;
        }
      }

      .message {
        color: #262626;
        flex: 1;
      }
    }
  }

  .test-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}
</style>
