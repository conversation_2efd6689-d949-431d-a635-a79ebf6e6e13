<template>
  <div class="file-uploader-demo">
    <h2>自定义文件上传组件演示</h2>
    
    <div class="demo-section">
      <h3>基本用法</h3>
      <p>选择文件后会在右下角显示自定义的文件上传列表，支持文件名显示、上传状态、进度条和操作按钮。</p>
      
      <!-- 使用自定义文件上传组件 -->
      <FileUploader 
        ref="fileUploaderRef"
        :multiple="true"
        :accept="'*'"
        :upload-api="mockUploadApi"
        :folder-id="0"
        @change="handleUploadChange"
      />
    </div>

    <div class="demo-section">
      <h3>功能说明</h3>
      <ul>
        <li><strong>文件名显示</strong>：显示完整文件名，过长时会自动截断并显示省略号</li>
        <li><strong>文件大小</strong>：自动格式化显示文件大小（B、KB、MB、GB）</li>
        <li><strong>上传状态</strong>：
          <ul>
            <li>等待上传 - 灰色时钟图标</li>
            <li>上传中 - 蓝色加载图标，显示进度百分比</li>
            <li>上传成功 - 绿色对勾图标</li>
            <li>上传失败 - 红色感叹号图标</li>
            <li>已暂停 - 黄色暂停图标</li>
          </ul>
        </li>
        <li><strong>进度条</strong>：上传过程中显示彩色进度条背景</li>
        <li><strong>操作按钮</strong>：
          <ul>
            <li>暂停/恢复上传</li>
            <li>重试失败的文件</li>
            <li>移除单个文件</li>
            <li>清空所有文件</li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="demo-section">
      <h3>状态统计</h3>
      <div class="stats">
        <div class="stat-item">
          <span class="label">总文件数：</span>
          <span class="value">{{ fileUploaderRef?.fileList?.length || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="label">上传中：</span>
          <span class="value">{{ fileUploaderRef?.uploadingCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="label">上传成功：</span>
          <span class="value">{{ fileUploaderRef?.successCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="label">上传失败：</span>
          <span class="value">{{ fileUploaderRef?.errorCount || 0 }}</span>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>模拟操作</h3>
      <div class="demo-actions">
        <Button @click="simulateUploadProgress" type="primary">
          模拟上传进度
        </Button>
        <Button @click="simulateUploadError" type="default">
          模拟上传失败
        </Button>
        <Button @click="simulateUploadSuccess" type="default">
          模拟上传成功
        </Button>
        <Button @click="addMockFile" type="dashed">
          添加模拟文件
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Button } from 'ant-design-vue';
import FileUploader from './file-uploader.vue';

const fileUploaderRef = ref();

// 模拟上传API
const mockUploadApi = (formData: any, options: any) => {
  return new Promise((resolve, reject) => {
    // 模拟上传过程
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 20;
      if (options.onUploadProgress) {
        options.onUploadProgress({ loaded: progress, total: 100 });
      }
      
      if (progress >= 100) {
        clearInterval(timer);
        // 随机成功或失败
        if (Math.random() > 0.3) {
          resolve({
            code: 200,
            data: { url: 'https://example.com/file.pdf' },
            msg: '上传成功'
          });
        } else {
          reject(new Error('模拟上传失败'));
        }
      }
    }, 200);
  });
};

// 处理上传变化
const handleUploadChange = (info: any) => {
  console.log('Upload change:', info);
};

// 模拟上传进度
const simulateUploadProgress = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    const file = fileList.find(f => f.status === 'waiting' || f.status === 'uploading');
    if (file) {
      file.customStatus = 'uploading';
      let progress = 0;
      const timer = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          file.percent = progress;
          file.customStatus = 'done';
          clearInterval(timer);
        } else {
          file.percent = progress;
        }
      }, 500);
    }
  }
};

// 模拟上传失败
const simulateUploadError = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    const file = fileList.find(f => f.status === 'waiting' || f.status === 'uploading');
    if (file) {
      file.customStatus = 'error';
      file.percent = 0;
    }
  }
};

// 模拟上传成功
const simulateUploadSuccess = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    const file = fileList.find(f => f.status !== 'done' && f.customStatus !== 'done');
    if (file) {
      file.customStatus = 'done';
      file.percent = 100;
    }
  }
};

// 添加模拟文件
const addMockFile = () => {
  const mockFile = {
    uid: `mock-${Date.now()}`,
    name: `模拟文件-${Date.now()}.pdf`,
    size: Math.floor(Math.random() * 10000000) + 1000000, // 1MB-10MB
    status: 'waiting',
    percent: 0,
    originFileObj: new File([''], `模拟文件-${Date.now()}.pdf`, { type: 'application/pdf' })
  };
  
  if (fileUploaderRef.value?.fileList) {
    fileUploaderRef.value.fileList.push(mockFile);
    // 显示上传列表
    if (fileUploaderRef.value.state) {
      fileUploaderRef.value.state.show = true;
    }
  }
};
</script>

<style lang="less" scoped>
.file-uploader-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #262626;
    margin-bottom: 24px;
  }

  .demo-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    h3 {
      color: #262626;
      margin-bottom: 16px;
      font-size: 16px;
    }

    p {
      color: #595959;
      margin-bottom: 16px;
      line-height: 1.6;
    }

    ul {
      color: #595959;
      line-height: 1.6;

      li {
        margin-bottom: 8px;
      }

      ul {
        margin-top: 8px;
        margin-left: 16px;
      }
    }
  }

  .stats {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;

    .stat-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      background: #fff;
      border-radius: 6px;
      border: 1px solid #e8e8e8;

      .label {
        color: #8c8c8c;
        margin-right: 8px;
      }

      .value {
        color: #262626;
        font-weight: 500;
      }
    }
  }

  .demo-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}
</style>
