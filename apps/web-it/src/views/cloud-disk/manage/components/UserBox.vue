<script lang="ts" setup>
import type { ScrollActionType, TreeActionType } from '@vben/fe-ui';
import type { Nullable } from '@vben/types';

import { nextTick, reactive, ref, toRefs, unref } from 'vue';

// import { createShare, createSingleShare, getShareUserList } from '@/api/workFlow/document';
import { BasicModal, BasicTree, FeEmpty, ScrollContainer, useModalInner } from '@vben/fe-ui';
import { $t as t } from '@vben/locales';

import { DeleteOutlined } from '@ant-design/icons-vue';
import { Avatar, InputSearch, message } from 'ant-design-vue';

import {
  editShareCloudDiskFileApi,
  getCloudDiskShareDetail,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
  shareCloudDiskFileApi,
} from '#/api';

interface State {
  treeData: any[];
  ableList: any[];
  documentIds: number[];
  pagination: any;
  selectedData: any[];
  nodeId: string;
  loading: boolean;
  isAsync: boolean;
  activeKey: string;
  finish: boolean;
  treeKey: number;
  isBatch: boolean;
}

const emit = defineEmits(['register', 'reload']);
// const apiUrl = ref(globSetting.apiUrl);
const treeRef = ref<Nullable<TreeActionType>>(null);
const infiniteBody = ref<Nullable<ScrollActionType>>(null);
const [registerModal, { changeLoading, changeOkLoading, closeModal }] = useModalInner(init);
const state = reactive<State>({
  treeData: [],
  ableList: [],
  documentIds: [],
  selectedData: [],
  nodeId: '-1',
  loading: false,
  isAsync: false,
  activeKey: '',
  pagination: {
    keyword: '',
    currentPage: 1,
    pageSize: 20,
  },
  finish: false,
  treeKey: Date.now(),
  isBatch: false,
});
const { treeData, ableList, selectedData, pagination, loading, isAsync, treeKey } = toRefs(state);

async function init(data: { ids: number[]; isBatch: boolean }) {
  changeLoading(true);
  state.isAsync = false;
  state.finish = false;
  state.documentIds = data.ids;
  state.isBatch = data.isBatch;
  state.treeData = [];
  state.ableList = [];
  state.selectedData = [];
  state.pagination.keyword = '';
  state.pagination.currentPage = 1;
  state.nodeId = '-1';
  state.activeKey = '1';
  initData();
  if (data.isBatch) return changeLoading(false);
  if (!state.documentIds[0]) return changeLoading(false);
  const res = await getCloudDiskShareDetail({ id: state.documentIds[0] });
  state.selectedData = await getUserInfoByIdsApi(res.shareUserIds);
  changeLoading(false);
}
function initData() {
  if (state.activeKey === '1') return getAllList();
  // if (state.activeKey === '2') {
  //   state.loading = true;
  //   getOrganization({ keyword: state.pagination.keyword, organId: '0' }).then((res) => {
  //     state.ableList = res.data;
  //     state.loading = false;
  //   });
  // }
  // if (state.activeKey === '3') {
  //   state.loading = true;
  //   getSubordinates(state.pagination.keyword).then((res) => {
  //     state.ableList = res.data;
  //     state.loading = false;
  //   });
  // }
  nextTick(() => {
    bindScroll();
  });
}
const iconList = {
  COMPANY: 'clarity:organization-line',
  DEPARTMENT: 'clarity:group-line',
  USER: 'lucide:user',
};
function getAllList() {
  state.loading = true;
  if (state.pagination.keyword) state.nodeId = '-1';
  getTreeList({ orgId: state.nodeId }).then((res) => {
    if (state.pagination.keyword) {
      // if (res.data.list.length < state.pagination.pageSize) state.finish = true;
      state.treeData = [...state.treeData, ...res];
    } else {
      state.treeData = res;
      if (state.treeData.length > 0 && state.nodeId === '-1') {
        getTree().setExpandedKeys([state.treeData[0].id]);
      }
    }
    state.loading = false;
  });
}
function bindScroll() {
  const bodyRef = infiniteBody.value;
  const vBody = bodyRef?.getScrollWrap();
  vBody?.addEventListener('scroll', () => {
    if (vBody.scrollHeight - vBody.clientHeight - vBody.scrollTop <= 200 && !state.loading && !state.finish) {
      state.pagination.currentPage += 1;
      getAllList();
    }
  });
}
// function onTabsChange() {
//   state.pagination.keyword = '';
//   state.nodeId = '-1';
//   state.isAsync = false;
//   initData();
// }
const getTreeList = async (params) => {
  if (params.orgId && params.orgId.includes('_')) {
    const idList = params.orgId.split('_');
    params.orgId = idList[idList.length - 1];
  }
  let api = getUserTreeListApi;
  if (params.keyword) {
    api = getUserListByKeywordApi;
    params.keywords = params.keyword.trim();
  }
  const res = await api(params);
  res.forEach((item) => {
    item.isLeaf = Boolean(item.isLeaf);
    item.icon = iconList[item.type];
    if (item.type !== 'USER') {
      item.id = `${item.type}_${item.id}`;
    }
  });
  return res;
};
function onLoadData(node) {
  state.nodeId = node.id;
  return new Promise((resolve: (value?: unknown) => void) => {
    getTreeList({ orgId: state.nodeId }).then((res) => {
      const list = res;
      getTree().updateNodeByKey(node.eventKey, { children: list, isLeaf: list.length === 0 });
      resolve();
    });
  });
}
function handleSelect(keys) {
  if (keys.length === 0) return;
  const data = getTree().getSelectedNode(keys[0]);
  if (data?.type !== 'USER') return;
  handleNodeClick(data);
}
function handleNodeClick(data) {
  const boo = state.selectedData.some((o) => o.id === data.id);
  if (boo) return;
  state.selectedData.push(data);
}
function handleSearch() {
  state.treeKey = Date.now();
  state.nodeId = '-1';
  state.treeData = [];
  state.ableList = [];
  state.pagination.currentPage = 1;
  state.isAsync = !!state.pagination.keyword;
  state.finish = false;
  if (state.isAsync && state.activeKey === '1') {
    nextTick(() => {
      bindScroll();
    });
  }
  initData();
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) throw new Error('tree is null!');
  return tree;
}
function removeAll() {
  state.selectedData = [];
}
function removeData(index: number) {
  state.selectedData.splice(index, 1);
}
function handleSubmit() {
  if (state.selectedData.length === 0) return message.error('请选择共享人员');
  changeOkLoading(true);
  const userIds = state.selectedData.map((o) => o.id);
  const api = state.isBatch ? shareCloudDiskFileApi : editShareCloudDiskFileApi;
  api({ ids: state.documentIds, userIds })
    .then(() => {
      message.success('共享成功');
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .finally(() => {
      changeOkLoading(false);
    });
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="共享文件"
    :width="800"
    @ok="handleSubmit"
    destroy-on-close
    class="transfer-modal"
  >
    <div class="transfer__body">
      <div class="transfer-pane left-pane">
        <div class="transfer-pane__tool">
          <InputSearch
            :placeholder="t('common.enterKeyword')"
            allow-clear
            v-model:value="pagination.keyword"
            @search="handleSearch"
          />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <!--<Tabs-->
          <!--  v-model:active-key="state.activeKey"-->
          <!--  :tab-bar-gutter="10"-->
          <!--  size="small"-->
          <!--  class="pane-tabs"-->
          <!--  @change="onTabsChange"-->
          <!--&gt;-->
          <!--  <TabPane key="1" tab="全部数据" />-->
          <!--  <TabPane key="2" tab="当前组织" />-->
          <!--  <TabPane key="3" tab="我的下属" />-->
          <!--</Tabs>-->
          <template v-if="state.activeKey === '1'">
            <BasicTree
              class="tree-main"
              :tree-data="treeData"
              :load-data="onLoadData"
              click-row-to-expand
              :field-names="{ title: 'fullName' }"
              @select="handleSelect"
              ref="treeRef"
              :loading="loading"
              :key="treeKey"
              v-if="!isAsync"
            />
            <ScrollContainer v-loading="loading && pagination.currentPage === 1" v-else ref="infiniteBody">
              <div
                v-for="item in treeData"
                :key="item.id"
                class="selected-item selected-item-user"
                @click="handleNodeClick(item)"
              >
                <div class="selected-item-main">
                  <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                  <div class="selected-item-text">
                    <p class="name">{{ item.fullName }}</p>
                    <p class="organize" :title="item.organ">{{ item.organ }}</p>
                  </div>
                </div>
              </div>
              <FeEmpty v-if="treeData.length === 0" />
            </ScrollContainer>
          </template>
          <ScrollContainer v-loading="loading" v-else>
            <div
              v-for="item in ableList"
              :key="item.id"
              class="selected-item selected-item-user"
              @click="handleNodeClick(item)"
            >
              <div class="selected-item-main">
                <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                <div class="selected-item-text">
                  <p class="name">{{ item.fullName }}</p>
                  <p class="organize" :title="item.organ">{{ item.organ }}</p>
                </div>
              </div>
            </div>
            <FeEmpty v-if="ableList.length === 0" />
          </ScrollContainer>
        </div>
      </div>
      <div class="transfer-pane right-pane">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <div v-for="(item, i) in selectedData" :key="i" class="selected-item selected-item-user">
              <div class="selected-item-main">
                <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                <div class="selected-item-text">
                  <p class="name">{{ item.fullName }}</p>
                  <p class="organize" :title="item.organ">{{ item.organ }}</p>
                </div>
                <DeleteOutlined class="delete-btn" @click="removeData(i)" />
              </div>
            </div>
            <FeEmpty v-if="selectedData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
