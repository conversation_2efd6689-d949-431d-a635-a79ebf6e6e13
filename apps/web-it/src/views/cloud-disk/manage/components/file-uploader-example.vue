<template>
  <div class="file-uploader-example">
    <h2>文件上传事件示例</h2>
    
    <div class="example-section">
      <h3>上传组件</h3>
      <FileUploader 
        ref="fileUploaderRef"
        :multiple="true"
        :accept="'*'"
        :upload-api="exampleUploadApi"
        :folder-id="0"
        @change="handleChange"
        @all-completed="handleAllCompleted"
        @all-success="handleAllSuccess"
      />
    </div>

    <div class="example-section">
      <h3>事件触发记录</h3>
      <div class="event-record">
        <div class="record-item" v-for="(record, index) in eventRecords" :key="index">
          <div class="record-header">
            <span class="event-name" :class="record.type">{{ record.type }}</span>
            <span class="event-time">{{ record.time }}</span>
          </div>
          <div class="record-content">
            <pre>{{ JSON.stringify(record.data, null, 2) }}</pre>
          </div>
        </div>
        <div v-if="eventRecords.length === 0" class="no-records">
          暂无事件记录
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>快速测试</h3>
      <div class="test-buttons">
        <Button @click="addTestFiles" type="primary">添加测试文件</Button>
        <Button @click="simulateAllSuccess" type="default">模拟全部成功</Button>
        <Button @click="simulatePartialFailure" type="default">模拟部分失败</Button>
        <Button @click="clearRecords" type="dashed">清空记录</Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Button } from 'ant-design-vue';
import FileUploader from './file-uploader.vue';

const fileUploaderRef = ref();
const eventRecords = ref<Array<{ type: string; time: string; data: any }>>([]);

// 添加事件记录
const addEventRecord = (type: string, data: any) => {
  eventRecords.value.unshift({
    type,
    time: new Date().toLocaleTimeString(),
    data
  });
  
  // 限制记录数量
  if (eventRecords.value.length > 10) {
    eventRecords.value = eventRecords.value.slice(0, 10);
  }
};

// 示例上传API
const exampleUploadApi = (formData: any, options: any) => {
  return new Promise((resolve, reject) => {
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 30;
      if (options.onUploadProgress) {
        options.onUploadProgress({ loaded: progress, total: 100 });
      }
      
      if (progress >= 100) {
        clearInterval(timer);
        // 随机成功或失败
        if (Math.random() > 0.2) {
          resolve({
            code: 200,
            data: { url: 'https://example.com/file.pdf' },
            msg: '上传成功'
          });
        } else {
          reject(new Error('模拟上传失败'));
        }
      }
    }, 200);
  });
};

// 处理文件变化
const handleChange = (info: any) => {
  addEventRecord('CHANGE', {
    fileName: info.file.name,
    status: info.file.status,
    percent: info.file.percent
  });
};

// 处理所有文件完成
const handleAllCompleted = (data: any) => {
  addEventRecord('ALL-COMPLETED', {
    total: data.total,
    successCount: data.successCount,
    errorCount: data.errorCount,
    message: `所有 ${data.total} 个文件上传完成！成功：${data.successCount}，失败：${data.errorCount}`
  });
};

// 处理所有文件成功
const handleAllSuccess = (data: any) => {
  addEventRecord('ALL-SUCCESS', {
    total: data.total,
    successCount: data.successCount,
    message: `🎉 恭喜！所有 ${data.total} 个文件都上传成功了！`
  });
};

// 添加测试文件
const addTestFiles = () => {
  const files = [
    { name: '文档1.pdf', size: 1024 * 1024 },
    { name: '图片1.jpg', size: 512 * 1024 },
    { name: '表格1.xlsx', size: 2 * 1024 * 1024 }
  ];
  
  files.forEach((fileInfo, index) => {
    const mockFile = {
      uid: `example-${Date.now()}-${index}`,
      name: fileInfo.name,
      size: fileInfo.size,
      status: 'waiting',
      percent: 0,
      originFileObj: new File([''], fileInfo.name, { type: 'application/octet-stream' })
    };
    
    if (fileUploaderRef.value?.fileList) {
      fileUploaderRef.value.fileList.push(mockFile);
      if (fileUploaderRef.value.state) {
        fileUploaderRef.value.state.show = true;
      }
    }
  });
};

// 模拟全部成功
const simulateAllSuccess = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    fileList.forEach(file => {
      file.customStatus = 'done';
      file.percent = 100;
    });
    
    setTimeout(() => {
      fileUploaderRef.value?.checkAllFilesSuccess?.();
    }, 200);
  }
};

// 模拟部分失败
const simulatePartialFailure = () => {
  const fileList = fileUploaderRef.value?.fileList;
  if (fileList && fileList.length > 0) {
    fileList.forEach((file, index) => {
      if (index === 0) {
        file.customStatus = 'error';
        file.percent = 0;
      } else {
        file.customStatus = 'done';
        file.percent = 100;
      }
    });
    
    setTimeout(() => {
      fileUploaderRef.value?.checkAllFilesSuccess?.();
    }, 200);
  }
};

// 清空记录
const clearRecords = () => {
  eventRecords.value = [];
};
</script>

<style lang="less" scoped>
.file-uploader-example {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #262626;
    margin-bottom: 24px;
  }

  .example-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;

    h3 {
      color: #262626;
      margin-bottom: 16px;
      font-size: 16px;
    }
  }

  .event-record {
    max-height: 400px;
    overflow-y: auto;

    .record-item {
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      margin-bottom: 12px;
      overflow: hidden;

      .record-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f5f5f5;
        border-bottom: 1px solid #e8e8e8;

        .event-name {
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;

          &.CHANGE {
            background: #e6f7ff;
            color: #1890ff;
          }

          &.ALL-COMPLETED {
            background: #f0f5ff;
            color: #2f54eb;
          }

          &.ALL-SUCCESS {
            background: #f6ffed;
            color: #52c41a;
          }
        }

        .event-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .record-content {
        padding: 12px;

        pre {
          margin: 0;
          font-size: 12px;
          color: #262626;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }

    .no-records {
      text-align: center;
      color: #8c8c8c;
      padding: 40px;
      font-size: 14px;
    }
  }

  .test-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}
</style>
