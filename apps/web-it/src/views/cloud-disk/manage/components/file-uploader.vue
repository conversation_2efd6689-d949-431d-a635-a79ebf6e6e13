<script setup lang="ts">
import { h, nextTick, reactive, ref } from 'vue';

import { CloudUploadOutlined } from '@ant-design/icons-vue';
import { Button, Upload } from 'ant-design-vue';

const UploadRef = ref();
const UploadBtnRef = ref();
const state = reactive({
  show: false,
});
const fileList = ref([]);
const handleChange = (info) => {
  console.log(info);
};
defineExpose({});
</script>

<template>
  <Upload ref="UploadRef" v-model:file-list="fileList" v-bind="$attrs" @change="handleChange">
    <Button :icon="h(CloudUploadOutlined)" type="primary"> 上传文件 </Button>
  </Upload>
  <div v-if="state.show" class="upload-list">
    <div class="p-2 text-lg">上传文件列表</div>
    <div class="px-2"></div>
  </div>
</template>

<style lang="less">
.upload-list {
  position: fixed;
  right: 32px;
  bottom: 32px;
  width: 600px;
  min-height: 300px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}
</style>
