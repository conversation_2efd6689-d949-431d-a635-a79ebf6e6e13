<script setup lang="ts">
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';

import { computed, h, reactive, ref } from 'vue';

import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  PaperClipOutlined,
  PauseOutlined,
} from '@ant-design/icons-vue';
import { Button, Upload } from 'ant-design-vue';

const props = defineProps({
  uploadApi: {
    type: Function,
    required: true,
  },
  folderId: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['change', 'allSuccess', 'allCompleted']);
const UploadRef = ref();
const state = reactive({
  show: false,
});

const fileList = ref([]);

// 文件状态图标映射
const statusIconMap = {
  waiting: { icon: ClockCircleOutlined, color: '#909398' },
  uploading: { icon: LoadingOutlined, color: '#1890ff' },
  done: { icon: CheckCircleOutlined, color: '#52c41a' },
  success: { icon: CheckCircleOutlined, color: '#52c41a' },
  error: { icon: ExclamationCircleOutlined, color: '#ff4d4f' },
  paused: { icon: PauseOutlined, color: '#faad14' },
};

// 文件状态文本映射
const statusTextMap = {
  waiting: '等待上传',
  uploading: '上传中',
  done: '上传成功',
  success: '上传成功',
  error: '上传失败',
  paused: '已暂停',
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`;
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / 1024 / 1024).toFixed(1)} MB`;
  } else {
    return `${(size / 1024 / 1024 / 1024).toFixed(1)} GB`;
  }
};

// 处理文件变化
const handleChange = (info) => {
  console.log('File change:', info);

  // 向外部发送 change 事件
  emit('change', info);

  // 如果有文件，显示上传列表
  if (fileList.value.length > 0) {
    state.show = true;
  }

  // 延迟检查所有文件是否上传完成（等待状态更新）
  setTimeout(() => {
    checkAllFilesSuccess();
  }, 100);
};

// 获取文件显示状态
const getFileDisplayStatus = (file: any) => {
  if (file.customStatus) {
    return file.customStatus;
  }
  return file.status || 'waiting';
};

// 检查是否所有文件都上传成功
const checkAllFilesSuccess = () => {
  if (fileList.value.length === 0) return;

  const allCompleted = fileList.value.every((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'done' || status === 'success' || status === 'error';
  });

  const allSuccess = fileList.value.every((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'done' || status === 'success';
  });

  // 如果所有文件都完成了上传（无论成功还是失败）
  if (allCompleted && fileList.value.length > 0) {
    const successFiles = fileList.value.filter((file) => {
      const status = getFileDisplayStatus(file);
      return status === 'done' || status === 'success';
    });

    const errorFiles = fileList.value.filter((file) => {
      const status = getFileDisplayStatus(file);
      return status === 'error';
    });

    // 触发所有文件完成事件
    console.log('所有文件上传完成！', {
      total: fileList.value.length,
      success: successFiles.length,
      error: errorFiles.length,
    });

    emit('allCompleted', {
      files: fileList.value,
      total: fileList.value.length,
      successFiles,
      errorFiles,
      successCount: successFiles.length,
      errorCount: errorFiles.length,
    });

    // 如果全部成功，还要触发全部成功事件
    if (allSuccess) {
      console.log('所有文件上传成功！', successFiles);
      emit('allSuccess', {
        files: successFiles,
        total: fileList.value.length,
        successCount: successFiles.length,
      });
    }
  }
};

// 文件操作方法
const pauseFile = (file: any) => {
  console.log('暂停文件:', file.name);
  // 设置自定义状态
  file.customStatus = 'paused';
  // 这里可以调用实际的暂停上传API
};

const resumeFile = (file: any) => {
  console.log('恢复文件:', file.name);
  // 恢复上传状态
  file.customStatus = 'uploading';
  // 这里可以调用实际的恢复上传API

  // 检查是否所有文件完成
  setTimeout(() => {
    checkAllFilesSuccess();
  }, 100);
};

const retryFile = (file: any) => {
  console.log('重试文件:', file.name);
  // 重置状态和进度
  file.customStatus = 'uploading';
  file.percent = 0;
  // 这里可以调用实际的重试上传API

  // 检查是否所有文件完成
  setTimeout(() => {
    checkAllFilesSuccess();
  }, 100);
};

const removeFile = (file: any) => {
  console.log('移除文件:', file.name);
  const index = fileList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }

  // 如果没有文件了，隐藏列表
  if (fileList.value.length === 0) {
    state.show = false;
  }

  // 检查是否所有文件完成
  setTimeout(() => {
    checkAllFilesSuccess();
  }, 100);
};

const clearAllFiles = () => {
  fileList.value = [];
  state.show = false;
};

// 计算属性
const uploadingCount = computed(() => {
  return fileList.value.filter((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'uploading';
  }).length;
});

const successCount = computed(() => {
  return fileList.value.filter((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'done' || status === 'success';
  }).length;
});

const errorCount = computed(() => {
  return fileList.value.filter((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'error';
  }).length;
});
const uploadFile = ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  const formData = { ...data };
  formData[filename] = file;
  // 使用框架上传方法已实现 FormData 不需要重复转换
  // const formData = new FormData();
  // if (data) {
  //   Object.keys(data).forEach((key) => {
  //     formData.append(key, data[key] as string);
  //   });
  // }
  // formData.append(filename, file);
  props
    .uploadApi(formData, {
      withCredentials,
      headers,
      params: {
        folderId: props.folderId,
      },
      onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
        if (onProgress) {
          onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
        }
      },
    })
    .then((response: unknown) => {
      console.log(response, 'res');
      if (onSuccess) {
        onSuccess(response);
      }
    })
    .catch((error) => {
      onError(error);
      console.log('Upload error:', error);
    });
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
defineExpose({
  fileList,
  clearAllFiles,
  checkAllFilesSuccess,
  pauseFile,
  resumeFile,
  retryFile,
  removeFile,
  uploadingCount,
  successCount,
  errorCount,
  state,
});
</script>

<template>
  <Upload
    ref="UploadRef"
    v-model:file-list="fileList"
    :custom-request="uploadFile"
    :show-upload-list="false"
    v-bind="$attrs"
    @change="handleChange"
  >
    <Button :icon="h(CloudUploadOutlined)" type="primary"> 上传文件 </Button>
  </Upload>

  <!-- 自定义文件上传列表 -->
  <div v-if="state.show" class="upload-list">
    <!-- 标题栏 -->
    <div class="upload-list-header">
      <div class="upload-list-title">
        <span>上传文件列表</span>
        <span class="file-count">({{ fileList.length }})</span>
      </div>
      <div class="upload-list-actions">
        <Button type="text" size="small" @click="clearAllFiles"> 清空列表 </Button>
        <Button type="text" size="small" @click="state.show = false">
          <CloseOutlined />
        </Button>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="upload-list-body">
      <div
        v-for="file in fileList"
        :key="file.uid"
        class="upload-file-item"
        :class="`status-${getFileDisplayStatus(file)}`"
      >
        <!-- 进度条背景 -->
        <div
          class="file-progress-bg"
          :style="{ width: getFileDisplayStatus(file) === 'uploading' ? `${file.percent || 0}%` : '0%' }"
        ></div>

        <!-- 文件信息 -->
        <div class="file-info">
          <!-- 文件图标和名称 -->
          <div class="file-name">
            <PaperClipOutlined class="file-icon" />
            <span class="name-text" :title="file.name">{{ file.name }}</span>
          </div>

          <!-- 文件大小和状态 -->
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
            <span class="file-status">
              <component
                :is="statusIconMap[getFileDisplayStatus(file)]?.icon || ClockCircleOutlined"
                :style="{ color: statusIconMap[getFileDisplayStatus(file)]?.color || '#909398' }"
                :spin="getFileDisplayStatus(file) === 'uploading'"
              />
              <span class="status-text">
                {{
                  getFileDisplayStatus(file) === 'uploading'
                    ? `${Math.round(file.percent || 0)}%`
                    : statusTextMap[getFileDisplayStatus(file)] || '等待上传'
                }}
              </span>
            </span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="file-actions">
          <!-- 移除按钮 -->
          <Button type="text" size="small" @click="removeFile(file)" title="移除" class="remove-btn">
            <CloseOutlined />
          </Button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="fileList.length === 0" class="empty-state">
        <CloudUploadOutlined class="empty-icon" />
        <p>暂无上传文件</p>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.upload-list {
  position: fixed;
  right: 32px;
  bottom: 32px;
  width: 600px;
  min-height: 300px;
  max-height: 500px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .upload-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 8px 8px 0 0;

    .upload-list-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;

      .file-count {
        color: #8c8c8c;
        font-weight: normal;
        margin-left: 4px;
      }
    }

    .upload-list-actions {
      display: flex;
      gap: 8px;
      align-items: center;

      .ant-btn {
        color: #8c8c8c;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .upload-list-body {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    .upload-file-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: 12px 20px;
      border-bottom: 1px solid #f5f5f5;
      transition: all 0.2s ease;
      overflow: hidden;

      &:hover {
        background-color: #fafafa;
      }

      &:last-child {
        border-bottom: none;
      }

      // 进度条背景
      .file-progress-bg {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background: linear-gradient(90deg, #e6f7ff 0%, #bae7ff 100%);
        transition: width 0.3s ease;
        z-index: 1;
      }

      // 状态样式
      &.status-success {
        .file-progress-bg {
          background: linear-gradient(90deg, #f6ffed 0%, #d9f7be 100%);
        }
      }

      &.status-error {
        .file-progress-bg {
          background: linear-gradient(90deg, #fff2f0 0%, #ffccc7 100%);
        }
      }

      &.status-paused {
        .file-progress-bg {
          background: linear-gradient(90deg, #fffbe6 0%, #fff1b8 100%);
        }
      }

      .file-info {
        flex: 1;
        position: relative;
        z-index: 2;
        min-width: 0;

        .file-name {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .file-icon {
            color: #8c8c8c;
            margin-right: 8px;
            flex-shrink: 0;
          }

          .name-text {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .file-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;

          .file-size {
            color: #8c8c8c;
          }

          .file-status {
            display: flex;
            align-items: center;
            gap: 4px;

            .anticon {
              font-size: 12px;
            }

            .status-text {
              color: #595959;
            }
          }
        }
      }

      .file-actions {
        position: relative;
        z-index: 2;
        display: flex;
        gap: 4px;
        margin-left: 12px;

        .ant-btn {
          width: 24px;
          height: 24px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #8c8c8c;

          &:hover {
            color: #1890ff;
          }

          &.remove-btn:hover {
            color: #ff4d4f;
          }

          .anticon {
            font-size: 12px;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #bfbfbf;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// 滚动条样式
.upload-list-body::-webkit-scrollbar {
  width: 6px;
}

.upload-list-body::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.upload-list-body::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}
</style>
