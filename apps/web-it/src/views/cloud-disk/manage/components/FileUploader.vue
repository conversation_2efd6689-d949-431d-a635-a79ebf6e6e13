<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref } from 'vue';
import VueSimpleUploader from 'vue-simple-uploader';
import 'vue-simple-uploader/dist/style.css';

import { FeEmpty } from '@vben/fe-ui';
// import { documentMerge } from '@/api/workFlow/document';
import FileItem from '@vben/fe-ui/components/Fe/Upload/src/SimpleUploader/FileItem.vue';
import { buildBitUUID } from '@vben/fe-ui/utils/uuid';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message } from 'ant-design-vue';
import SparkMD5 from 'spark-md5';

const props = defineProps({
  parentId: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(['fileSuccess']);
const Uploader = VueSimpleUploader.Uploader;
const UploaderBtn = VueSimpleUploader.UploaderBtn;
const UploaderUnsupport = VueSimpleUploader.UploaderUnsupport;
const UploaderList = VueSimpleUploader.UploaderList;
const UploaderFile = VueSimpleUploader.UploaderFile;

const uploaderRef = ref<any>(null);
const uploaderBtnRef = ref<any>(null);
const options = reactive({
  // target: `${globSetting.apiUrl}/api/file/chunk`,
  target: `${import.meta.env.VITE_GLOB_API_URL}/infra/oss/config/debug`,
  chunkSize: 1024 * 1024 * 5,
  maxChunkRetries: 5,
  singleFile: false,
  testChunks: true, // 是否开启服务器分片校验
  // 服务器分片校验函数，秒传及断点续传基础
  checkChunkUploadedByResponse(chunk, message) {
    const objMessage = JSON.parse(message);
    if (objMessage.code === 200) {
      if (objMessage.data.uploaded) {
        return true;
      }
      const chunkNumbers = objMessage.data.chunkNumbers;
      return (chunkNumbers || []).includes(chunk.offset + 1);
    } else {
      return true;
    }
  },
  headers: {
    Authorization: '',
  },
  query: {
    fileType: '',
    extension: '',
  },
});
const attrs = { accept: '*' };
const statusText = {
  success: '上传成功',
  error: '上传失败',
  uploading: '上传中',
  paused: '暂停中',
  waiting: '等待中',
};
const panelShow = ref(false);
const uploadKey = ref(Date.now());

defineExpose({ openUploader });

function openUploader() {
  uploaderBtnRef.value.$el.click();
}
function onFileAdded(file) {
  // 自定义状态
  file.customStatus = 'check';
  panelShow.value = true;
  options.query.fileType = file.fileType;
  options.query.extension = file.getExtension();
  computeMD5(file);
}
const calculateSliceMd5 = (file) => {
  if (!file) {
    return;
  }
  // isCalculating.value = true

  const fileReader = new FileReader();
  // 定义切片大小为 256 KB
  const chunkSize = 256 * 1024;
  // 切割文件，只取从开头到 chunkSize 的部分
  const chunk = file.slice(0, chunkSize);
  const spark = new SparkMD5.ArrayBuffer();

  // FileReader 读取完成后的回调
  fileReader.addEventListener('load', (e) => {
    // 将读取到的 ArrayBuffer 数据追加到 spark 实例中
    if ("result" in e.target) {
      spark.append(e.target.result);
    }
    // 计算完成，获取最终的MD5值（十六进制）
    const sliceMD5 = spark.end();
    console.log(`文件总大小: ${(file.value.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`前256KB MD5: ${sliceMD5}`);
  });

  fileReader.onerror = (e) => {
    console.error(e);
  };

  // 以 ArrayBuffer 的格式读取文件切片
  fileReader.readAsArrayBuffer(chunk);
};
/**
 * 计算md5，实现断点续传及秒传
 * @param file
 */
function computeMD5(file) {
  const fileReader = new FileReader();
  const blobSlice = File.prototype.slice || (File.prototype as any).mozSlice || (File.prototype as any).webkitSlice;
  let currentChunk = 0;
  const chunkSize = 10 * 1024 * 1000;
  const chunks = Math.ceil(file.size / chunkSize);
  const spark = new SparkMD5.ArrayBuffer();

  file.pause();
  loadNext();

  fileReader.addEventListener('load', (e) => {
    spark.append(e.target?.result);
    if (currentChunk < chunks) {
      currentChunk++;
      loadNext();
    } else {
      const md5 = spark.end();
      computeMD5Success(md5, file);
    }
  });
  fileReader.onerror = function () {
    message.error(`文件${file.name}读取出错，请检查该文件`);
    file.cancel();
  };

  function loadNext() {
    const start = currentChunk * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end));
  }
}
function computeMD5Success(md5, file) {
  file.uniqueIdentifier = md5 + buildBitUUID(); // 把md5值+随机码作为文件的识别码
  file.customStatus = 'uploading';
  file.resume(); // 开始上传
}
function onFileSuccess(_rootFile, file, response, _chunk) {
  const res = JSON.parse(response);
  if (res.code != 200) {
    message.error(res.msg);
    file.cancel();
    return;
  }
  setTimeout(() => {
    // 秒传 直接展示
    if (res.data.uploaded) {
      // 秒传结果
    } else if (res.data.merge) {
      // 需要合并
      handleSuccess(file);
    } else {
      // 上传错误
      file.cancel();
      message.error(`上传失败`);
    }
  }, 300);
}
function onFileProgress(_rootFile, _file, _chunk) {}
function onFileError(_rootFile, file, _response, _chunk) {
  file.cancel();
  message.error(`上传失败`);
}
function handleSuccess(file) {
  const query = {
    identifier: file.uniqueIdentifier,
    fileName: file.name.replaceAll('#', ''),
    fileSize: file.size,
    fileType: file.getType(),
    extension: file.getExtension(),
    parentId: props.parentId,
  };
  file.customCompleted = true;
  emit('fileSuccess', {});
  file.cancel();
  // documentMerge(query)
  //   .then((res) => {
  //     // 自定义完成状态
  //     file.customCompleted = true;
  //     emit('fileSuccess', res.data);
  //     file.cancel();
  //   })
  //   .catch(() => {
  //     file.cancel();
  //   });
}
function onComplete() {
  uploaderRef.value?.uploader.cancel();
  panelShow.value = false;
  uploadKey.value = Date.now();
}

onMounted(() => {
  nextTick(() => {
    (window as any).uploader = uploaderRef.value?.uploader;
  });
});
</script>

<template>
  <div id="document-file-uploader">
    <Uploader
      :options="options"
      :file-status-text="statusText"
      class="uploader-app"
      ref="uploaderRef"
      @file-added="onFileAdded"
      @file-success="onFileSuccess"
      @file-progress="onFileProgress"
      @file-error="onFileError"
      @complete="onComplete"
      :auto-start="false"
      :key="uploadKey"
    >
      <UploaderUnsupport />
      <UploaderBtn id="file-uploader-btn" ref="uploaderBtnRef" :attrs="attrs">选择文件</UploaderBtn>
      <UploaderList v-show="panelShow">
        <template #default="{ fileList }">
          <div class="file-panel">
            <div class="file-title">
              <p class="title">上传文件列表</p>
              <div class="operate flex cursor-pointer items-center text-base">
                <VbenIcon icon="ion:close" @click="onComplete" />
                <!--<i class="icon-ym icon-ym-nav-close" @click="onComplete"></i>-->
              </div>
            </div>
            <div class="upload-file-list">
              <div class="upload-file-list__item" v-for="file in fileList" :key="file.id">
                <UploaderFile :class="`file_${file.id}`" ref="files" :file="file" :list="true">
                  <template #default="props">
                    <FileItem :file="props.file" :list="props.list" />
                  </template>
                </UploaderFile>
              </div>
              <FeEmpty description="暂无待上传文件" v-if="fileList.length === 0" />
            </div>
          </div>
        </template>
      </UploaderList>
    </Uploader>
  </div>
</template>
<style lang="less">
@import '#/style/index.less';
#document-file-uploader {
  position: fixed;
  z-index: 20;
  right: 10px;
  bottom: 10px;

  .uploader-app {
    width: 600px;
  }
  .file-panel {
    background-color: @component-background;
    border: 1px solid @border-color-base;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

    .file-title {
      display: flex;
      justify-content: space-between;
      height: 50px;
      line-height: 50px;
      padding: 0 15px;
      border-bottom: 1px solid @border-color-base;
      font-size: 16px;

      .operate {
        text-align: right;
      }
    }
    .upload-file-list {
      position: relative;
      height: 240px;
      overflow-x: hidden;
      overflow-y: auto;
      background-color: @component-background;
      font-size: 14px;

      > li {
        background-color: @component-background;
      }
      .uploader-file {
        line-height: 48px !important;
        height: 49px !important;
        border-bottom: 1px solid @border-color-base;
        .uploader-file-info {
          padding: 0 10px;
          line-height: 48px;
        }
      }
    }
  }
  .uploader-file-icon {
    &:before {
      content: '' !important;
    }
  }

  .uploader-file-actions > span {
    margin-right: 6px;
  }
  .uploader-app .upload-file-list .upload-file-list__item {
    margin-top: 0;
    border-radius: 0;
  }
}

/* 隐藏上传按钮 */
#file-uploader-btn {
  position: absolute;
  clip: rect(0, 0, 0, 0);
}
</style>
