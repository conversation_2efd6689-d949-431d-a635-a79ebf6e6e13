<script setup lang="ts">
import type { PageListParams, Pagination } from '@vben/types';

import type { DriveFileInfo } from '#/api';

import { computed, h, onMounted, ref, watch } from 'vue';

import { confirm, Page } from '@vben/common-ui';
import { BasicForm, BasicTable, FeEmpty, ScrollContainer, useForm, useModal, useTable } from '@vben/fe-ui';
import { toFileSize } from '@vben/fe-ui/utils/fe';
import { $t as t } from '@vben/locales';

import { VbenIcon } from '@vben-core/shadcn-ui';

import {
  CloudDownloadOutlined,
  CloudUploadOutlined,
  DeleteOutlined,
  DeliveredProcedureOutlined,
  EditOutlined,
  FolderAddOutlined,
  ShareAltOutlined,
  UndoOutlined,
} from '@ant-design/icons-vue';
import {
  Breadcrumb,
  BreadcrumbItem,
  Button,
  Checkbox,
  CheckboxGroup,
  message,
  Space,
  TabPane,
  Tabs,
  Tooltip,
} from 'ant-design-vue';

import {
  cancelCloudDiskShareApi,
  delCloudDiskFileApi,
  downloadFileApi,
  getCloudDiskMySharePageListApi,
  getCloudDiskPageListApi,
  getCloudDiskRecyclePageListApi,
  getCloudDiskShareToMePageListApi,
  permanentDelApi,
  restoreApi,
} from '#/api';
import audioImg from '#/assets/images/document/audio.png';
import blankImg from '#/assets/images/document/blank.png';
import codeImg from '#/assets/images/document/code.png';
import excelImg from '#/assets/images/document/excel.png';
import folderImg from '#/assets/images/document/folder.png';
import imageImg from '#/assets/images/document/image.png';
import pdfImg from '#/assets/images/document/pdf.png';
import pptImg from '#/assets/images/document/ppt.png';
import rarImg from '#/assets/images/document/rar.png';
import txtImg from '#/assets/images/document/txt.png';
import wordImg from '#/assets/images/document/word.png';
import { createImgPreview } from '#/components/Preview';
import { downloadByUrl } from '#/utils/file/download';

import FileUploader from './components/file-uploader.vue';
import FolderTree from './components/FolderTree.vue';
import Form from './components/Form.vue';
import Preview from './components/Preview.vue';
import UserBox from './components/UserBox.vue';

const activeKey = ref<'all' | 'shareOut' | 'shareTome' | 'trash'>('all');
const leftList = [
  { id: 'all', fileName: '我的文档', icon: 'akar-icons:file' },
  { id: 'shareOut', fileName: '我的共享', icon: 'gg:share' },
  { id: 'shareTome', fileName: '共享给我', icon: 'ri:user-received-line' },
  { id: 'trash', fileName: '回收站', icon: 'streamline:recycle-bin-2-solid' },
];
const levelList = ref<{ fileName?: string; id: number }[]>([]);
const searchInfo = ref<PageListParams & { keyword: string; parentId: number }>({
  current: 1,
  size: 20,
  keyword: '',
  parentId: 0,
});
const handleJump = (item: { id: number }, i: number) => {
  searchInfo.value.parentId = item.id;
  levelList.value = levelList.value.slice(0, i + 1);
  handleReset();
};
const handleReset = () => {
  searchInfo.value.keyword = '';
  initData();
};
const loading = ref(false);
const pageList = ref<Pagination<DriveFileInfo>>({
  records: [],
  current: 1,
  size: 20,
  total: 0,
});
const selectedRowKeys = ref<number[]>([]);
const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowSelection: { type: 'checkbox' },
  clickToRowSelect: false,
  showTableSetting: false,
  pagination: true,
  immediate: false,
  resizeHeightOffset: 10,
});
const checkAll = ref(false);
const isIndeterminate = ref(false);
// 缩略模式下单选
const handleCheckedChange = (val: any[]) => {
  const checkedCount = val.length;
  checkAll.value = !!checkedCount && checkedCount === pageList.value.records.length;
  isIndeterminate.value = !!checkedCount && checkedCount < pageList.value.records.length;
};
const initData = () => {
  loading.value = true;
  selectedRowKeys.value = [];
  clearSelectedRowKeys();
  handleCheckedChange(selectedRowKeys.value);
  const api = {
    all: getCloudDiskPageListApi,
    shareOut: getCloudDiskMySharePageListApi,
    shareTome: getCloudDiskShareToMePageListApi,
    trash: getCloudDiskRecyclePageListApi,
  };
  api[activeKey.value](searchInfo.value).then((res) => {
    pageList.value = res;
    loading.value = false;
  });
};
const [registerForm, { resetFields }] = useForm({
  baseColProps: { span: 8 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
  schemas: [
    {
      field: 'keyword',
      label: t('common.keyword'),
      component: 'Input',
      componentProps: {
        placeholder: t('common.enterKeyword'),
        submitOnPressEnter: true,
      },
    },
  ],
});
function handleSubmit(values: { keyword?: string }) {
  searchInfo.value.keyword = values?.keyword || '';
  if (searchInfo.value.keyword) resetBreadcrumb();
  initData();
}
function resetBreadcrumb() {
  const activeItem = leftList.find((o) => o.id === activeKey.value);
  levelList.value = [{ id: 0, fileName: activeItem?.fileName }];
  searchInfo.value.parentId = 0;
}
function addFolder() {
  openFormModal(true, { parentId: searchInfo.value.parentId });
}
const [registerFormModal, { openModal: openFormModal }] = useModal();
const fileUploaderRef = ref();
// 上传文件
function uploadFile() {
  // fileUploaderRef.value?.openUploader();
  fileUploaderRef.value?.pickFile();
}
const showMode = ref(1);
// 切换展示模式
function toggleShowMode(type: number) {
  showMode.value = type;
  handleCheckedChange(selectedRowKeys.value);
}
const [registerUserBox, { openModal: openUserBox }] = useModal();
// 批量共享
function handleShare() {
  openUserBox(true, { ids: selectedRowKeys.value, isBatch: true });
}
// 下载
function handleDownload() {
  downloadFileApi({ ids: selectedRowKeys.value }).then((res) => {
    downloadByUrl({ url: res.url, fileName: res.name });
  });
}
// 删除
async function handleDelete() {
  await confirm('您确定要把所选文件放入回收站, 是否继续?', t('common.tipTitle'));
  delCloudDiskFileApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('删除成功');
    initData();
  });
}
function handleRename() {
  openFormModal(true, { parentId: searchInfo.value.parentId, id: selectedRowKeys.value[0] });
}
const [registerFolderTree, { openModal: openFolderTree }] = useModal();
// 移动到
function handleMoveTo() {
  openFolderTree(true, { ids: selectedRowKeys.value, parentId: searchInfo.value.parentId });
}
// 取消共享
async function handleUnshare() {
  await confirm('您确定要取消共享, 是否继续?', t('common.tipTitle'));
  cancelCloudDiskShareApi({ ids: selectedRowKeys.value }).then(() => {
    message.success('取消成功');
    initData();
  });
}
// 还原
async function handleRecovery() {
  await confirm('您确定要还原选中的文件?', t('common.tipTitle'));
  restoreApi(selectedRowKeys.value).then(() => {
    message.success('还原成功');
    initData();
  });
}
// 彻底删除
async function handleTrashDel() {
  await confirm('文件删除后将无法恢复，您确定要彻底删除所选文件吗?', '提示');
  permanentDelApi(selectedRowKeys.value).then((res) => {
    message.success(res.msg);
    initData();
  });
}
function onSelectionChange({ keys }: { keys: number[] }) {
  selectedRowKeys.value = keys;
}
const allColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '', dataIndex: 'isShare', width: 35 },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '创建日期', dataIndex: 'createTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
const shareOutColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '共享日期', dataIndex: 'shareTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '操作', dataIndex: 'action', width: 50 },
];
const shareTomeColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '共享日期', dataIndex: 'shareTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '共享人员', dataIndex: 'creatorUserId', width: 120 },
];
const trashColumns = [
  { title: '文件名', dataIndex: 'fileName' },
  { title: '大小', dataIndex: 'fileSize', width: 90 },
  { title: '删除日期', dataIndex: 'deleteTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
function getColumns() {
  if (activeKey.value === 'shareOut') return shareOutColumns;
  if (activeKey.value === 'shareTome') return shareTomeColumns;
  if (activeKey.value === 'trash') return trashColumns;
  return allColumns;
}
const getTableBindValue = computed(() => ({
  loading: loading.value,
  onSelectionChange,
  dataSource: pageList.value.records,
  columns: getColumns(),
  pagination: {
    current: pageList.value.current,
    pageSize: pageList.value.size,
    total: pageList.value.total,
    onChange: (page: number, pageSize: number) => {
      console.log(page, pageSize, 'page, pageSize');
      searchInfo.value.current = page;
      searchInfo.value.size = pageSize;
      initData();
    },
  },
}));
const imgTypeList = new Set(['bmp', 'gif', 'jpeg', 'jpg', 'png']);
const wordTypeList = ['doc', 'docx'];
const excelTypeList = ['xls', 'xlsx'];
const pptTypeList = ['ppt', 'pptx'];
const pdfTypeList = ['pdf'];
const zipTypeList = new Set(['7z', 'arj', 'rar', 'z', 'zip']);
const txtTypeList = new Set(['log', 'txt']);
const codeTypeList = new Set(['cs', 'html', 'xml']);
const previewTypeList = new Set([...excelTypeList, ...pdfTypeList, ...pptTypeList, ...wordTypeList]);
const videoTypeList = new Set([
  'avi',
  'avi',
  'flv',
  'flv',
  'mkv',
  'mov',
  'mp3',
  'mp4',
  'mpeg',
  'mpg',
  'mpg',
  'ram',
  'rm',
  'rm',
  'rmvb',
  'swf',
  'wma',
  'wmv',
]);
const filePreviewRef = ref();
// 预览
function handlePreview(record: DriveFileInfo) {
  // 图片预览
  if (record.fileExtension && imgTypeList.has(record.fileExtension)) {
    const imageList = [record.uploaderUrl];
    createImgPreview({ imageList });
    return;
  }
  if (record.fileExtension && previewTypeList.has(record.fileExtension)) {
    // 文件预览
    const file = {
      name: record.fileName,
      fileId: record.filePath,
      fileVersionId: null,
      url: record.uploaderUrl,
    };
    filePreviewRef.value?.init(file);
  }
}
function openFolder(record: DriveFileInfo) {
  if (record.id) {
    searchInfo.value.parentId = record.id;
    levelList.value.push({ id: record.id, fileName: record.fileName });
    selectedRowKeys.value = [];
    handleReset();
  }
}
function onRecordClick(record: DriveFileInfo) {
  if (['shareOut', 'trash'].includes(activeKey.value)) return;
  record.type ? handlePreview(record) : openFolder(record);
}
function getRecordImg(ext?: string) {
  if (!ext) return folderImg;
  if (ext) ext = ext.replace('.', '');
  if (wordTypeList.includes(ext)) return wordImg;
  if (excelTypeList.includes(ext)) return excelImg;
  if (pptTypeList.includes(ext)) return pptImg;
  if (pdfTypeList.includes(ext)) return pdfImg;
  if (zipTypeList.has(ext)) return rarImg;
  if (txtTypeList.has(ext)) return txtImg;
  if (codeTypeList.has(ext)) return codeImg;
  if (imgTypeList.has(ext)) return imageImg;
  if (videoTypeList.has(ext)) return audioImg;
  return blankImg;
}
// 单个共享
function handleSingleShare(id: number) {
  if (!id) return;
  openUserBox(true, { ids: [id], isBatch: false });
}
// 缩略模式下全选
function handleCheckAllChange(e) {
  const val = e.target.checked;
  selectedRowKeys.value = val ? pageList.value.records.map((o) => o.id as number) : [];
  isIndeterminate.value = false;
}
watch(
  () => activeKey.value,
  () => init(),
);
function init() {
  resetBreadcrumb();
  resetFields();
}

onMounted(() => {
  init();
});
</script>

<template>
  <Page auto-content-height>
    <div class="document-wrapper flex h-full bg-white">
      <Tabs v-model:active-key="activeKey" tab-position="left" class="mr-3 h-full border-r">
        <TabPane v-for="tab in leftList" :key="tab.id">
          <template #tab>
            <div class="flex items-center">
              <VbenIcon :icon="tab.icon" class="mr-1" />
              <span>{{ tab.fileName }}</span>
            </div>
          </template>
        </TabPane>
      </Tabs>
      <div class="document-container">
        <Breadcrumb class="mb-3">
          <BreadcrumbItem
            v-if="levelList.length > 1"
            @click="handleJump(levelList[levelList.length - 2], levelList.length - 2)"
          >
            <a>返回上一级</a>
          </BreadcrumbItem>
          <BreadcrumbItem v-for="(item, i) in levelList" :key="i">
            <span v-if="i + 1 >= levelList.length">{{ item.fileName }}</span>
            <a v-else @click="handleJump(item, i)">{{ item.fileName }}</a>
          </BreadcrumbItem>
        </Breadcrumb>
        <div class="fe-common-search-box">
          <BasicForm class="search-form" @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
          <div class="fe-common-search-box-right">
            <template v-if="selectedRowKeys.length === 0">
              <template v-if="activeKey === 'all'">
                <Button :icon="h(FolderAddOutlined)" @click="addFolder()" class="mr-2">新建文件夹</Button>
                <FileUploader ref="fileUploaderRef" class="mr-2" />
                <!--<Button :icon="h(CloudUploadOutlined)" type="primary" @click="uploadFile()" class="mr-2">-->
                <!--  上传文件-->
                <!--</Button>-->
              </template>
              <Tooltip>
                <template #title>{{ showMode === 1 ? '缩略模式' : '列表模式' }}</template>
                <VbenIcon
                  icon="ooui:image-layout-thumbnail"
                  class="cursor-pointer text-3xl"
                  @click="toggleShowMode(2)"
                  v-show="showMode === 1"
                />
                <VbenIcon
                  icon="fluent-mdl2:thumbnail-view"
                  class="cursor-pointer text-3xl"
                  @click="toggleShowMode(1)"
                  v-show="showMode === 2"
                />
              </Tooltip>
            </template>
            <template v-else>
              <Space.Compact block>
                <template v-if="activeKey === 'all'">
                  <Button :icon="h(ShareAltOutlined)" @click="handleShare">共享</Button>
                  <Button :icon="h(CloudDownloadOutlined)" @click="handleDownload">下载</Button>
                  <Button :icon="h(DeleteOutlined)" @click="handleDelete">删除</Button>
                  <Button :icon="h(EditOutlined)" @click="handleRename" v-if="selectedRowKeys.length === 1">
                    重命名
                  </Button>
                  <Button :icon="h(DeliveredProcedureOutlined)" @click="handleMoveTo">移动</Button>
                </template>
                <template v-if="activeKey === 'shareOut'">
                  <Button :icon="h(ShareAltOutlined)" @click="handleUnshare" v-if="levelList.length <= 1">
                    取消共享
                  </Button>
                </template>
                <template v-if="activeKey === 'shareTome'">
                  <Button :icon="h(CloudDownloadOutlined)" @click="handleDownload">下载</Button>
                </template>
                <template v-if="activeKey === 'trash'">
                  <Button :icon="h(UndoOutlined)" @click="handleRecovery">还原</Button>
                  <Button :icon="h(DeleteOutlined)" @click="handleTrashDel">删除</Button>
                </template>
              </Space.Compact>
            </template>
          </div>
        </div>
        <BasicTable @register="registerTable" v-bind="getTableBindValue" v-show="showMode === 1">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fileName'">
              <span class="document-fileName" :class="{ 'link-fullName': record.type }" @click="onRecordClick(record)">
                <img :src="getRecordImg(record.fileExtension)" class="file-img" alt="" />
                {{ record.fileName }}
              </span>
            </template>
            <template v-if="column.key === 'isShare'">
              <span v-if="record.isShare" title="共享文件">
                <VbenIcon icon="gg:share" />
              </span>
              <span v-else></span>
            </template>
            <template v-if="column.key === 'fileSize'">
              {{ toFileSize(record.fileSize) }}
            </template>
            <template v-if="column.key === 'action'">
              <Button
                type="link"
                size="small"
                @click="handleSingleShare(record.id)"
                class="!px-0"
                v-if="levelList.length <= 1"
              >
                共享
              </Button>
            </template>
          </template>
        </BasicTable>
        <div class="document-list-header" v-show="showMode === 2">
          <Checkbox
            :indeterminate="isIndeterminate"
            v-model:checked="checkAll"
            :disabled="pageList.records.length === 0"
            @change="handleCheckAllChange"
          >
            全选
          </Checkbox>
        </div>
        <CheckboxGroup
          v-model:value="selectedRowKeys"
          class="document-list"
          @change="handleCheckedChange"
          v-show="showMode === 2"
        >
          <ScrollContainer v-loading="loading">
            <div class="document-list-main">
              <div
                class="document-item"
                :class="{ active: record.id && selectedRowKeys.includes(record.id) }"
                v-for="record in pageList.records"
                :key="record.id"
                @click="onRecordClick(record)"
              >
                <img :src="getRecordImg(record.fileExtension)" class="document-item-img" />
                <p class="document-item-title" :title="record.fileName">{{ record.fileName }}</p>
                <div class="check-icon" @click.stop>
                  <Checkbox :value="record.id" />
                </div>
              </div>
            </div>
            <FeEmpty v-if="pageList.records.length === 0" />
          </ScrollContainer>
        </CheckboxGroup>
      </div>
    </div>
    <Form @register="registerFormModal" @reload="initData" />
    <!--<FileUploader ref="fileUploaderRef" :parent-id="searchInfo.parentId" @file-success="initData" />-->
    <UserBox @register="registerUserBox" @reload="initData" />
    <FolderTree @register="registerFolderTree" @reload="initData" />
    <Preview ref="filePreviewRef" />
  </Page>
</template>

<style lang="less">
@import '#/style/index.less';
.document-wrapper {
  :deep(.ant-table-container),
  .ant-table-container {
    .ant-table-cell::before {
      display: none !important;
    }
  }
  .ant-tabs-content-holder {
    display: none;
  }
  .document-container {
    flex: 1;
    padding-top: 20px;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .fe-common-search-box {
    margin-bottom: 10px;
    position: relative;
    .fe-common-search-box-right {
      position: absolute;
      right: 10px;
      top: 0;
      display: flex;
      align-items: center;
      .mode-icon {
        margin-left: 10px;
        font-size: 18px;
        line-height: 32px;
        cursor: pointer;
        &:hover {
          color: @primary-color;
        }
      }
    }
  }
  .document-list-header {
    margin-top: -10px;
    margin-right: 10px;
    line-height: 40px;
    flex-shrink: 0;
    border-bottom: 1px solid @border-color-base1;
  }
  .document-list {
    flex: 1;
    width: 100%;
    overflow: hidden;
    padding-bottom: 10px;
    .document-list-main {
      padding: 20px 10px 0 0;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-content: flex-start;
      flex-wrap: wrap;
    }
    .document-item {
      width: 100px;
      height: 100px;
      border-radius: var(--border-radius);
      overflow: hidden;
      margin: 0 20px 40px;
      padding: 5px;
      cursor: pointer;
      position: relative;
      &:hover {
        background-color: @app-content-background;
        .check-icon {
          display: block;
        }
      }
      &.active {
        .check-icon {
          display: block;
        }
      }
      .document-item-img {
        width: 60px;
        height: 60px;
        margin: 0 auto 6px;
      }
      .document-item-title {
        color: @text-color-label;
        font-size: 14px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .check-icon {
        position: absolute;
        top: 2px;
        right: 4px;
        display: none;
      }
    }
  }
  .document-fileName {
    cursor: pointer;
    &.link-fullName {
      &:hover {
        color: @primary-color;
      }
    }
    .file-img {
      width: 16px;
      height: 16px;
      display: inline-block;
      vertical-align: -3px;
    }
  }
}
</style>
